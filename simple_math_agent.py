from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.prebuilt import create_react_agent
from loguru import logger
import os
from dotenv import load_dotenv
load_dotenv()
from langchain_google_genai import GoogleGenerativeAIEmbeddings
api_key = os.getenv("GEMINI_API_KEY")
from langchain_postgres.vectorstores import PGVector
model=ChatGoogleGenerativeAI(model="gemini-2.0-flash", api_key=api_key)
from sqlalchemy import create_engine
from langchain.tools.retriever import create_retriever_tool
credential_path = "gen-lang-client-0077680037-9adbc1746e59.json"
if not os.path.exists(credential_path):
    raise FileNotFoundError(f"Credential file '{credential_path}' not found.")
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credential_path

# Then in your embeddings initialization
def initialize_embeddings():
    try:
        
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/text-embedding-004",
     
        )
        return embeddings
    except Exception as e:
        logger.error(f"Failed to initialize Google Generative AI embeddings: {str(e)}")
        raise RuntimeError(f"Failed to initialize embeddings: {str(e)}")
    

def get_engine():
    """Returns a SQLAlchemy engine instance for PGVector compatibility"""
    username = os.getenv('db_user')      # Fixed string concatenation
    password = os.getenv('db_password')  # Fixed string concatenation
    hostname = os.getenv('db_host')      # Fixed string concatenation
    port = os.getenv('db_port')          # Fixed string concatenation
    database_name = os.getenv('db_name') # Fixed string concatenation
    
    # Create the SQLAlchemy engine
    engine = create_engine(f'postgresql+psycopg://{username}:{password}@{hostname}:{port}/{database_name}')
    return engine
   
    
embedding = initialize_embeddings()
def create_vectorstore(collection_name=None):
    collection_name = collection_name or os.getenv("collection_name")
    
    return PGVector(
        embeddings=embedding,
        collection_name=collection_name,
        connection=get_engine(),
        use_jsonb=True
    )
def create_retrievers(collection_name=None):
    try:
        vectorstore = create_vectorstore(collection_name)
        retriever = vectorstore.as_retriever(
            search_kwargs={"k": 10}  # Increase k to 10
        )     
    except Exception as e:
        raise RuntimeError(f"Failed to create retriever: {str(e)}")

retriever = create_retrievers()


tools = [create_retriever_tool(
            retriever=retriever,
            name="Document_Retriever",
            description="Searches and returns documents from the database according to the query" 
        )]

system_prompt = """

Answer the following questions as best you can. You have access to the following tools:
{tools}
Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought:{agent_scratchpad}"""

memory = InMemorySaver()

agent = create_react_agent(
    model=model,
    tools=tools,
    prompt=system_prompt,
    checkpointer=memory,
)

agent_config = {"configurable": {"thread_id": "default_user"}}
