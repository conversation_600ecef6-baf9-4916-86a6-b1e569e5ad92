import asyncio
import base64
import json
import os
import pathlib
from typing import AsyncGenerator, Literal
from dotenv import load_dotenv
load_dotenv()
import gradio as gr
import numpy as np
from fastapi import FastAPI
from fastapi.responses import HTMLResponse
from fastrtc import (
    AsyncStream<PERSON>and<PERSON>,
    Stream,
    get_cloudflare_turn_credentials_async,
    wait_for_item,
)
from google import genai
# Removed unused imports - keeping only what's needed
from gradio.utils import get_space
from pydantic import BaseModel

# Vector store imports
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.prebuilt import create_react_agent
from langchain_postgres.vectorstores import PGVector
from sqlalchemy import create_engine
from langchain.tools.retriever import create_retriever_tool
from loguru import logger

# Audio processing imports (for future TTS implementation)
# import io
# import wave
# import tempfile

current_dir = pathlib.Path(__file__).parent

# Vector store configuration
credential_path = "gen-lang-client-0077680037-9adbc1746e59.json"
if not os.path.exists(credential_path):
    raise FileNotFoundError(f"Credential file '{credential_path}' not found.")
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credential_path

def initialize_embeddings():
    """Initialize Google Generative AI embeddings"""
    try:
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/text-embedding-004",
        )
        return embeddings
    except Exception as e:
        logger.error(f"Failed to initialize Google Generative AI embeddings: {str(e)}")
        raise RuntimeError(f"Failed to initialize embeddings: {str(e)}")

def get_engine():
    """Returns a SQLAlchemy engine instance for PGVector compatibility"""
    username = os.getenv('db_user')
    password = os.getenv('db_password')
    hostname = os.getenv('db_host')
    port = os.getenv('db_port')
    database_name = os.getenv('db_name')

    # Create the SQLAlchemy engine
    engine = create_engine(f'postgresql+psycopg://{username}:{password}@{hostname}:{port}/{database_name}')
    return engine

def create_vectorstore(collection_name=None):
    """Create PGVector vectorstore instance"""
    collection_name = collection_name or os.getenv("collection_name")
    embedding = initialize_embeddings()

    return PGVector(
        embeddings=embedding,
        collection_name=collection_name,
        connection=get_engine(),
        use_jsonb=True
    )

def create_retrievers(collection_name=None):
    """Create retriever from vectorstore"""
    try:
        vectorstore = create_vectorstore(collection_name)
        retriever = vectorstore.as_retriever(
            search_kwargs={"k": 10}
        )
        return retriever
    except Exception as e:
        raise RuntimeError(f"Failed to create retriever: {str(e)}")

def create_agent_with_tools():
    """Create LangGraph agent with vector store tools"""
    api_key = os.getenv("GEMINI_API_KEY")
    model = ChatGoogleGenerativeAI(model="gemini-2.0-flash-exp", api_key=api_key)

    retriever = create_retrievers()
    tools = [create_retriever_tool(
        retriever=retriever,
        name="Document_Retriever",
        description="Use this tool to help user regarding queries related to the company"
    )]

    system_prompt = """
    You are Chaman, a helpful assistant with a cool personality.
    You answer user queries using the tools available to you.
    Always use the tools when user asks question related to a person or company.
    Keep your responses concise and conversational for voice interaction.
    """

    memory = InMemorySaver()

    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=system_prompt,
        checkpointer=memory,
    )

    return agent, {"configurable": {"thread_id": "default_user"}}



def encode_audio(data: np.ndarray) -> str:
    """Encode Audio data to send to the server"""
    return base64.b64encode(data.tobytes()).decode("UTF-8")


class GeminiWithVectorStoreHandler(AsyncStreamHandler):
    """Enhanced handler that combines Gemini live API with vector store tools"""

    def __init__(
        self,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 24000,
    ) -> None:
        super().__init__(
            expected_layout,
            output_sample_rate,
            input_sample_rate=16000,
        )
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()

        # Initialize agent with vector store tools
        try:
            self.agent, self.agent_config = create_agent_with_tools()
            logger.info("✅ Vector store agent initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize vector store agent: {str(e)}")
            self.agent = None
            self.agent_config = None

    def copy(self) -> "GeminiWithVectorStoreHandler":
        return GeminiWithVectorStoreHandler(
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
        )

    async def start_up(self):
        if not self.phone_mode:
            await self.wait_for_args()
            api_key, voice_name = self.latest_args[1:]
        else:
            api_key, voice_name = None, "Puck"

        # Store configuration for later use
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        self.voice_name = voice_name

        logger.info(f"🎤 Handler started with voice: {self.voice_name}")
        logger.info(f"🤖 Vector store agent available: {self.agent is not None}")

    async def process_with_agent(self, text_input: str) -> str:
        """Process text input using the vector store agent"""
        if not self.agent:
            return "Sorry, the vector store agent is not available."

        try:
            # Use the agent to process the input
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.agent.invoke(
                    {"messages": [{"role": "user", "content": text_input}]},
                    config=self.agent_config
                )
            )

            # Extract the response text
            if response and "messages" in response:
                return response["messages"][-1].content
            else:
                return "I couldn't process your request properly."

        except Exception as e:
            logger.error(f"Error processing with agent: {str(e)}")
            return "Sorry, I encountered an error while processing your request."

    async def speech_to_text_gemini(self, audio_data: bytes) -> str:
        """Convert speech to text using Gemini"""
        try:
            client = genai.Client(
                api_key=self.api_key,
                http_options={"api_version": "v1alpha"},
            )

            # Use Gemini for speech-to-text
            # This is a simplified approach - you might need to adjust based on Gemini's STT API
            response = await client.aio.models.generate_content_async(
                model="gemini-2.0-flash-exp",
                contents=[{
                    "parts": [{
                        "inline_data": {
                            "mime_type": "audio/wav",
                            "data": base64.b64encode(audio_data).decode()
                        }
                    }]
                }]
            )

            return response.text if response.text else ""

        except Exception as e:
            logger.error(f"Error in speech-to-text: {str(e)}")
            return ""

    async def process_audio_input(self, audio_data: bytes) -> None:
        """Process incoming audio data"""
        try:
            # Convert speech to text
            text_input = await self.speech_to_text_gemini(audio_data)

            if text_input.strip():
                logger.info(f"👂 Transcribed: {text_input}")

                # Process with vector store agent
                response_text = await self.process_with_agent(text_input)
                logger.info(f"🤖 Agent response: {response_text}")

                # For now, we'll put the text response in the queue
                # In a real implementation, you'd convert this to speech
                # Using a TTS service like ElevenLabs, OpenAI TTS, etc.

                # Create a simple audio response (silence for now)
                # You should replace this with actual TTS
                silence_duration = len(response_text) * 0.1  # Rough estimate
                silence_samples = int(self.output_sample_rate * silence_duration)
                silence_audio = np.zeros((1, silence_samples), dtype=np.int16)

                await self.output_queue.put((self.output_sample_rate, silence_audio))

        except Exception as e:
            logger.error(f"Error processing audio input: {str(e)}")

    async def stream(self) -> AsyncGenerator[bytes, None]:
        """Audio stream for processing - not used in this implementation"""
        while not self.quit.is_set():
            try:
                audio = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio
            except (asyncio.TimeoutError, TimeoutError):
                pass

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio frame and process it"""
        _, array = frame
        array = array.squeeze()

        # Convert numpy array to bytes for processing
        audio_bytes = array.tobytes()

        # Process the audio input asynchronously
        asyncio.create_task(self.process_audio_input(audio_bytes))

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit processed audio response"""
        return await wait_for_item(self.output_queue)

    def shutdown(self) -> None:
        """Shutdown the handler"""
        self.quit.set()


stream = Stream(
    modality="audio",
    mode="send-receive",
    handler=GeminiWithVectorStoreHandler(),
    rtc_configuration=get_cloudflare_turn_credentials_async if get_space() else None,
    concurrency_limit=5 if get_space() else None,
    time_limit=90 if get_space() else None,
    additional_inputs=[
        gr.Textbox(
            label="API Key",
            type="password",
            value=os.getenv("GEMINI_API_KEY") if not get_space() else "",
        ),
        gr.Dropdown(
            label="Voice",
            choices=[
                "Puck",
                "Charon",
                "Kore",
                "Fenrir",
                "Aoede",
            ],
            value="Puck",
        ),
    ],
)


class InputData(BaseModel):
    webrtc_id: str
    voice_name: str
    api_key: str


app = FastAPI()

stream.mount(app)


@app.post("/input_hook")
async def _(body: InputData):
    stream.set_input(body.webrtc_id, body.api_key, body.voice_name)
    return {"status": "ok"}


@app.get("/")
async def index():
    rtc_config = await get_cloudflare_turn_credentials_async() if get_space() else None
    html_content = (current_dir / "index.html").read_text()
    html_content = html_content.replace("__RTC_CONFIGURATION__", json.dumps(rtc_config))
    return HTMLResponse(content=html_content)


if __name__ == "__main__":
    import os

    if (mode := os.getenv("MODE")) == "UI":
        stream.ui.launch(server_port=7860)
    elif mode == "PHONE":
        stream.fastphone(token=os.getenv("HF_TOKEN"), host="0.0.0.0", port=7860)
    else:
        import uvicorn

        uvicorn.run(app, host="0.0.0.0", port=7860)